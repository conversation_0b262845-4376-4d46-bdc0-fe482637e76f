const Webpack = require('webpack');
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const FriendlyErrorsWebpackPlugin = require('@soda/friendly-errors-webpack-plugin');
const ESLintWebpackPlugin = require('eslint-webpack-plugin');
const { VueLoaderPlugin } = require('vue-loader');
const AutoImport = require('unplugin-auto-import/webpack');
const Components = require('unplugin-vue-components/webpack');
const f2eci = require('../f2eci.json');

const isBuild = process.env.NODE_ENV === 'production';
function resolve(dir) {
  return path.resolve(process.cwd(), dir);
}

/**
 * @type {Webpack.Configuration}
 */
const config = {
  mode: 'production',
  output: {
    clean: true,
    path: resolve('build'),
    filename: 'js/[name].[contenthash].js',
    chunkFilename: 'js/[name].[contenthash].js',
    publicPath: process.env.PUBLIC_PATH + '/' || '/',
  },
  resolve: {
    extensions: ['.js', '.ts', '.json', '.vue'],
    alias: {
      '@': resolve('src'),
      vue$: '@vue/runtime-dom',
    },
    fallback: {
      path: require.resolve('path-browserify'),
    },
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        use: ['vue-loader'],
      },
      {
        test: /\.(js|ts)$/,
        use: ['babel-loader'],
        include: [resolve('src')],
      },
      {
        test: /\.(jpe?g|gif|svg)(\?.*)?$/,
        type: 'asset/resource',
        exclude: /src\/assets\/svg/,
        generator: {
          filename: 'static/img/[name].[hash:12][ext]',
        },
      },
      {
        test: /\.png$/,
        type: 'asset/inline',
      },
      {
        test: /\.svg$/,
        include: /src\/assets\/svg/,
        use: [
          {
            loader: 'svg-sprite-loader',
          },
        ],
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        type: 'asset/resource',
        generator: {
          filename: 'static/font/[name].[hash:12][ext]',
        },
      },
      {
        include: /node_modules/,
        test: /\.mjs$/,
        // test: /\.js$/,
        type: 'javascript/auto',
      },
      {
        test: /\.css$/i,
        use: [
          isBuild
            ? {
                loader: MiniCssExtractPlugin.loader,
                options: {
                  publicPath: '../',
                },
              }
            : 'style-loader',
          'css-loader',
        ],
      },
      {
        test: /\.module\.scss$/,
        use: [
          isBuild
            ? {
                loader: MiniCssExtractPlugin.loader,
                options: {
                  publicPath: '../',
                },
              }
            : 'style-loader',
          {
            loader: 'css-loader',
            options: {
              modules: {
                localIdentName: '[name]__[local]--[hash:base64:5]', // 自定义类名的格式
              },
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: ['postcss-preset-env'],
              },
            },
          },
          {
            loader: 'sass-loader',
            options: {
              additionalData: '@import "@/styles/util.scss";@import "@/styles/variable.scss";',
            },
          },
        ],
      },
      {
        test: /\.scss$/,
        exclude: /\.module\.scss$/,
        use: [
          isBuild
            ? {
                loader: MiniCssExtractPlugin.loader,
                options: {
                  publicPath: '../',
                },
              }
            : 'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 2,
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: ['postcss-preset-env'],
              },
            },
          },
          {
            loader: 'sass-loader',
            options: {
              additionalData: '@import "@/styles/util.scss";@import "@/styles/variable.scss";',
            },
          },
        ],
      },
    ],
  },
  plugins: [
    new ESLintWebpackPlugin({
      extensions: ['ts', 'vue'],
    }),
    new VueLoaderPlugin(),
    new FriendlyErrorsWebpackPlugin(),
  ],
};

module.exports = config;
// export default config;
