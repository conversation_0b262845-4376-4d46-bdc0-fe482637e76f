<template>
  <div v-if="Array.isArray(pretools) && pretools.length" class="pretools-card">
    <div class="pretools-header" @click="toggleList">
      <div class="pretools-title">
        AI推理依据
        <span class="toggle-icon" @click="toggleList">
          <svg v-if="listExpanded" width="14" height="14" viewBox="0 0 1024 1024">
            <path d="M768 384l-256 256-256-256z" fill="#bdbdbd" />
          </svg>
          <svg v-else width="14" height="14" viewBox="0 0 1024 1024">
            <path d="M384 768l256-256-256-256z" fill="#bdbdbd" />
          </svg>
        </span>
      </div>
    </div>
    <transition name="fade">
      <div v-show="listExpanded" class="pretools-list">
        <div v-for="(tool, idx) in pretools" :key="idx" class="pretools-item">
          <div class="tool-row" :class="{ clickable: true }" @click="toggleExpand(idx)">
            <span class="tool-type">{{ tool.tool }}</span>
            <!-- <span class="tool-step">Step {{ tool.toolStep }}</span> -->
            <span
              class="tool-status"
              :class="{
                finished: tool.toolStatus === 'FINISHED',
                running: tool.toolStatus === 'RUNNING',
                error: tool.toolStatus === 'ERROR',
              }"
            >
              {{ toolStatusText(tool.toolStatus) }}
            </span>
            <span class="expand-icon">
              <svg v-if="isExpanded(idx)" width="14" height="14" viewBox="0 0 1024 1024">
                <path d="M768 384l-256 256-256-256z" fill="#bdbdbd" />
              </svg>
              <svg v-else width="14" height="14" viewBox="0 0 1024 1024">
                <path d="M384 768l256-256-256-256z" fill="#bdbdbd" />
              </svg>
            </span>
          </div>
          <transition name="fade">
            <div v-if="isExpanded(idx)" class="tool-detail">
              <div v-if="tool.tool_input && tool.tool_input.search_word_list" class="tool-field">
                <span class="field-label">检索词：</span>
                <span class="field-value">{{ tool.tool_input.search_word_list.join('，') }}</span>
              </div>
              <div v-if="tool.tool_input && tool.tool_input.reason" class="tool-field">
                <span class="field-label">推理说明：</span>
                <span class="field-value">{{ tool.tool_input.reason }}</span>
              </div>
              <div v-if="tool.tool_input && tool.tool_input.extra" class="tool-field">
                <span class="field-label">其他信息：</span>
                <span class="field-value">{{ tool.tool_input.extra }}</span>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';

interface IPretoolInput {
  search_word_list?: string[];
  reason?: string;
  extra?: string;
  [key: string]: unknown;
}

interface IPretool {
  tool: string;
  toolStep: number;
  toolStatus: string;
  tool_input?: IPretoolInput;
  [key: string]: unknown;
}

const props = defineProps<{ pretools?: IPretool[] }>();

const expandedIds = ref<number[]>([]);
const listExpanded = ref(false);

// 切换列表展开/折叠
function toggleList() {
  listExpanded.value = !listExpanded.value;
}

// 自动展开折叠：回答时展开，完成后折起
watch(
  () => props.pretools?.map((t) => t.toolStatus) ?? [],
  (statuses) => {
    if (statuses.some((s) => s === 'RUNNING')) {
      listExpanded.value = true;
    } else if (statuses.length && statuses.every((s) => s === 'FINISHED' || s === 'ERROR')) {
      listExpanded.value = false;
    }
  },
  { immediate: true },
);

function toggleExpand(idx: number) {
  const i = expandedIds.value.indexOf(idx);
  if (i > -1) {
    expandedIds.value.splice(i, 1);
  } else {
    expandedIds.value.push(idx);
  }
}
function isExpanded(idx: number) {
  return expandedIds.value.includes(idx);
}

function toolStatusText(status: string) {
  switch (status) {
    case 'FINISHED':
      return '已完成';
    case 'RUNNING':
      return '进行中';
    case 'ERROR':
      return '出错';
    default:
      return status;
  }
}
</script>

<style scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.pretools-card {
  background: var(--bg-glass-popup);
  border: 2px solid var(--border-accent);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm) var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  max-width: 90%;
  backdrop-filter: blur(10px);
  transition: box-shadow 0.18s;
  box-shadow: var(--shadow-medium);
}
.pretools-card:hover {
  box-shadow: transparent;
}

.pretools-item .tool-row.clickable {
  cursor: pointer;
  user-select: none;
}
.expand-icon {
  margin-left: auto; /* 推动图标到工具行末尾 */
  display: inline-flex;
  align-items: center;
  transform: translateX(8px); /* 额外右移 */
}
.tool-detail {
  padding: 10px 0 6px 0;
  border-top: 1px dashed #e6eaf3;
  margin-top: 6px;
  margin-left: 18px; /* 向右缩进 */
  display: flex;
  flex-direction: column;
  gap: 7px;
  font-size: 14px;
  color: #222;
}

.tool-field {
  display: flex;
  gap: var(--spacing-xs);
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.field-label {
  color: var(--text-tertiary);
  min-width: 70px;
  font-size: var(--font-size-md);
  font-weight: 500;
}

.field-value {
  color: var(--text-primary);
  word-break: break-all;
  font-size: var(--font-size-md);
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-8px);
}

.pretools-title {
  font-weight: 600;
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
}

.pretools-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pretools-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.pretools-item {
  position: relative;
  font-weight: 300;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 10px 16px 7px 16px;
  margin-bottom: 4px;
  box-shadow: transparent;
  color: #222;
  transition: box-shadow 0.15s;
}
.pretools-item .tool-row.clickable:hover {
  background: rgba(240, 240, 255, 0.13);
  border-radius: 6px;
}

.tool-row {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 2px;
  font-size: 14px;
  color: #222;
}

.tool-type {
  flex: 1;
  word-wrap: break-word;
  margin-bottom: 4px;
  font-weight: 320;
  color: #3d3d3d;
  font-size: 14px;
}

.tool-step {
  display: none;
  font-size: 13px;
  color: #8b9cb6;
  font-weight: 320;
  white-space: nowrap;
}

.tool-status {
  position: absolute;
  top: 40%;
  right: 8px;
  transform: translateY(-50%);
  font-size: 12px;
  display: inline-block;
  white-space: nowrap;
  padding: 1px 6px;
  border-radius: 8px;
  background: rgba(230, 240, 255, 0.55);
  color: #4e6ef2;
  font-weight: 320;
}
.tool-status.finished {
  background: #e6f7e6;
  color: #52c41a;
}
.tool-status.running {
  background: #fffbe6;
  color: #faad14;
}
.tool-status.error {
  background: #fff1f0;
  color: #f5222d;
}
.tool-desc {
  font-size: 14px;
  color: #555;
  margin-left: 2px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.tool-reason {
  margin-left: 0.5em;
}

.toggle-icon {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  vertical-align: middle;
}
</style>
